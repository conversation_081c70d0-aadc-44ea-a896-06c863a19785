import{r as n,a as js,b as Xe}from"./vendor-280e31ee.js";import{N as ls,u as Ke,a as Ns,L as vs,O as ks,b as Ss,B as As,R as Ps,c as oe}from"./router-208768c5.js";import{C as Cs,X as Ds,A as Rs,I as $s,M as Ts,U as Pe,S as Qe,L as Es,a as Ve,b as Ls,T as we,c as ke,B as os,d as Ce,e as is,f as _s,E as Us,g as Fs,h as Bs,F as ss,i as He,H as ts,D as de,j as Ms,k as Is,P as ve,R as ee,l as cs,m as De,n as Os,o as Hs,p as zs,q as ze,r as Ge,s as Ks,t as Vs,u as ds,K as qs,v as Ws,w as Oe,x as ms,y as Js,z as Ys}from"./icons-9d7a79a3.js";import{R as Se,B as us,C as Re,X as $e,Y as Te,T as Ae,a as xs,L as Ee,b as Ze,c as es,P as Gs,d as Xs,e as Qs,S as as,f as Zs}from"./charts-2d8bc326.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))y(i);new MutationObserver(i=>{for(const m of i)if(m.type==="childList")for(const d of m.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&y(d)}).observe(document,{childList:!0,subtree:!0});function r(i){const m={};return i.integrity&&(m.integrity=i.integrity),i.referrerPolicy&&(m.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?m.credentials="include":i.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function y(i){if(i.ep)return;i.ep=!0;const m=r(i);fetch(i.href,m)}})();var hs={exports:{}},qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var et=n,st=Symbol.for("react.element"),tt=Symbol.for("react.fragment"),at=Object.prototype.hasOwnProperty,rt=et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,nt={key:!0,ref:!0,__self:!0,__source:!0};function gs(s,l,r){var y,i={},m=null,d=null;r!==void 0&&(m=""+r),l.key!==void 0&&(m=""+l.key),l.ref!==void 0&&(d=l.ref);for(y in l)at.call(l,y)&&!nt.hasOwnProperty(y)&&(i[y]=l[y]);if(s&&s.defaultProps)for(y in l=s.defaultProps,l)i[y]===void 0&&(i[y]=l[y]);return{$$typeof:st,type:s,key:m,ref:d,props:i,_owner:rt.current}}qe.Fragment=tt;qe.jsx=gs;qe.jsxs=gs;hs.exports=qe;var e=hs.exports,ps,rs=js;ps=rs.createRoot,rs.hydrateRoot;const fs=()=>window.location.origin,ys=async(s,l={})=>{const r=localStorage.getItem("adminToken"),y=fs(),i={headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}}},m={...i,...l,headers:{...i.headers,...l.headers}},d=await fetch(`${y}${s}`,m);if(d.status===401){let w="Authentication failed. Please login again.";try{const T=await d.json();T.message&&T.message.includes("expired")?w="Your session has expired. Please login again.":T.message&&T.message.includes("token")&&(w="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:w}})),new Error(w)}return d},bs=n.createContext(),fe=()=>{const s=n.useContext(bs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},lt=({children:s})=>{const[l,r]=n.useState(!1),[y,i]=n.useState(null),[m,d]=n.useState(!0),[w,T]=n.useState(null),j=(N="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",N),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),T(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),r(!1),i(null),window.toast&&window.toast(N,"warning"),window.location.href="/login"};n.useEffect(()=>{const N=localStorage.getItem("adminToken"),p=localStorage.getItem("adminUser"),c=localStorage.getItem("redirectAfterLogin");N&&p&&(r(!0),i(JSON.parse(p))),c&&T(c),d(!1);const b=$=>{const{message:A}=$.detail;j(A)};return window.addEventListener("auth-error",b),()=>{window.removeEventListener("auth-error",b)}},[]);const S={isAuthenticated:l,user:y,login:async(N,p)=>{try{const c=fs();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${c}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${N?N.substring(0,3)+"***":"undefined"}`);const b=new AbortController,$=setTimeout(()=>b.abort(),3e4),A=await fetch(`${c}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:N,password:p}),signal:b.signal});clearTimeout($),console.log(`[AUTH] Response status: ${A.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(A.headers.entries()));const M=await A.text();console.log("[AUTH] Response text (first 200 chars):",M.substring(0,200));let L;try{L=JSON.parse(M),console.log("[AUTH] Parsed response data:",{success:L.success,message:L.message})}catch(R){console.error("[AUTH] Failed to parse response as JSON:",R),console.error("[AUTH] Raw response text:",M);let P="Server returned invalid response. Please try again.";return M.includes("Internal Server Error")?P="Server is experiencing issues. Please try again in a few moments.":M.includes("timeout")?P="Request timed out. Please check your connection and try again.":M.includes("Database")?P="Database connection issue. Please try again.":A.status>=500?P="Server error occurred. Please try again later.":A.status===404&&(P="Login service not found. Please contact support."),{success:!1,message:P}}if(A.ok&&L.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",L.token),localStorage.setItem("adminUser",JSON.stringify(L.user)),r(!0),i(L.user);const R=localStorage.getItem("redirectAfterLogin");return R?(localStorage.removeItem("redirectAfterLogin"),T(null),{success:!0,redirectTo:R}):{success:!0}}else return console.log(`[AUTH] Login failed: ${L.message||"Unknown error"}`),{success:!1,message:L.message||"Login failed. Please check your credentials."}}catch(c){console.error("[AUTH] Login error:",{name:c.name,message:c.message,stack:c.stack});let b="Login failed. Please try again.";return c.name==="AbortError"?b="Request timed out. Please check your connection and try again.":c.message.includes("fetch")?b="Network error. Please check your connection and try again.":c.message.includes("NetworkError")&&(b="Network connection failed. Please try again."),{success:!1,message:b}}},logout:(N=!1)=>{N&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),T(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),r(!1),i(null)},autoLogout:j,loading:m,redirectPath:w};return e.jsx(bs.Provider,{value:S,children:s})},ot=({message:s,type:l="info",duration:r=3e3,onClose:y})=>{const[i,m]=n.useState(!0);n.useEffect(()=>{const j=setTimeout(()=>{m(!1),setTimeout(y,300)},r);return()=>clearTimeout(j)},[r,y]);const d={success:Cs,error:Ds,warning:Rs,info:$s},w={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},T=d[l];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${i?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${w[l]}`,children:[e.jsx(T,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},it=({children:s})=>{const[l,r]=n.useState([]),y=(m,d="info",w=3e3)=>{const T=Date.now();r(j=>[...j,{id:T,message:m,type:d,duration:w}])},i=m=>{r(d=>d.filter(w=>w.id!==m))};return Xe.useEffect(()=>{window.toast=y},[]),e.jsxs(e.Fragment,{children:[s,l.map(m=>e.jsx(ot,{message:m.message,type:m.type,duration:m.duration,onClose:()=>i(m.id)},m.id))]})},ct=({children:s})=>{const{isAuthenticated:l,loading:r}=fe();return r?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):l?s:e.jsx(ls,{to:"/login",replace:!0})},dt=()=>{const[s,l]=n.useState(!1),r=n.useRef(null),{logout:y,user:i}=fe(),m=Ke();n.useEffect(()=>{const j=f=>{r.current&&!r.current.contains(f.target)&&l(!1)};return document.addEventListener("mousedown",j),()=>{document.removeEventListener("mousedown",j)}},[]);const d=()=>{y(),m("/login"),l(!1)},w=j=>{m(j),l(!1)},T=()=>{const j=[{label:"Profile",icon:Pe,onClick:()=>w("/profile")}];return((i==null?void 0:i.role)==="admin"||(i==null?void 0:i.role)==="superadmin")&&j.push({label:"Settings",icon:Qe,onClick:()=>w("/settings")}),j.push({label:"Logout",icon:Es,onClick:d,className:"text-red-600 hover:bg-red-50"}),j};return e.jsxs("div",{className:"relative",ref:r,children:[e.jsx("button",{onClick:()=>l(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(Ts,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:T().map((j,f)=>{const F=j.icon;return e.jsxs("button",{onClick:j.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${j.className||"text-gray-700"}`,children:[e.jsx(F,{className:"h-4 w-4 mr-3"}),j.label]},f)})})]})},mt=s=>{let l=0;if(s.length===0)return l.toString();for(let r=0;r<s.length;r++){const y=s.charCodeAt(r);l=(l<<5)-l+y,l=l&l}return Math.abs(l).toString(16)},ut=(s,l=32)=>`https://www.gravatar.com/avatar/${mt(s)}?s=${l}&d=identicon`,xt=()=>{const{user:s}=fe(),l=Ns(),[r,y]=n.useState(!1),i=()=>{const m=[{name:"Dashboard",path:"/dashboard",icon:Ls},{name:"Plugin Rank",path:"/plugin-rank",icon:we},{name:"Keyword Analysis",path:"/keyword-analysis",icon:ke},{name:"Plugin Data Analysis",path:"/analytics",icon:os}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&m.push({name:"Team Members",path:"/users",icon:Ce}),m};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[r&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>y(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${r?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>y(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(Ve,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:i().map(m=>{const d=m.icon,w=l.pathname===m.path;return e.jsxs(vs,{to:m.path,onClick:()=>y(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${w?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(d,{className:"h-5 w-5 mr-3"}),m.name]},m.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||ut((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(dt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ks,{})})})]})},ht=()=>{const[s,l]=n.useState(""),[r,y]=n.useState(""),[i,m]=n.useState(!1),[d,w]=n.useState(!1),[T,j]=n.useState(""),{login:f}=fe(),F=Ke(),S=async N=>{N.preventDefault(),j(""),w(!0);const p=await f(s,r);if(p.success){const c=p.redirectTo||"/dashboard";F(c)}else j(p.message||"Login failed");w(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:S,className:"space-y-6",children:[T&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:T}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(is,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:N=>l(N.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(_s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:i?"text":"password",value:r,onChange:N=>y(N.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>m(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i?e.jsx(Us,{className:"h-5 w-5"}):e.jsx(Fs,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:d,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:d?"Signing in...":"Sign In"})]})]})})})},ge=({isOpen:s,onClose:l,title:r,children:y,maxWidth:i="max-w-xl",fixedHeight:m=!1})=>(n.useEffect(()=>{const d=w=>{w.key==="Escape"&&l()};return s&&(document.addEventListener("keydown",d),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",d),document.body.style.overflow="unset"}},[s,l]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:l}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${i} w-full mx-4 transform transition-all ${m?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r}),e.jsx("button",{onClick:l,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(Ve,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${m?"flex-1 overflow-y-auto":""}`,children:y})]})]})}):null),gt=({isOpen:s,onClose:l,plugin:r})=>{const y=Ke(),i=n.useRef(null),[m,d]=n.useState([]),[w,T]=n.useState([]),[j,f]=n.useState(0),[F,S]=n.useState(0),[N,p]=n.useState([]),[c,b]=n.useState({}),[$,A]=n.useState(null),[M,L]=n.useState({}),[R,P]=n.useState(""),[h,g]=n.useState(""),[v,C]=n.useState(!1),[U,_]=n.useState(!0),[I,O]=n.useState(!0),[H,Y]=n.useState(!0),[G,Q]=n.useState(!0),[ae,me]=n.useState(0),re=(a,k)=>{me(k)},ye=async()=>{if(r)try{_(!0);const a=localStorage.getItem("adminToken"),E=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${r.slug}?days=15`,{headers:{Authorization:`Bearer ${a}`}});if(!E.ok)throw new Error("Failed to fetch download data");const D=await E.json();if(D.success&&D.downloadData){const W=D.downloadData.map(J=>({date:new Date(J.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:J.downloads,fullDate:J.date}));d(W)}else d([])}catch(a){console.error("Error fetching download data:",a),d([])}finally{_(!1)}},ue=async()=>{if(r)try{O(!0);const a=localStorage.getItem("adminToken"),E=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${r.slug}`,{headers:{Authorization:`Bearer ${a}`}});if(!E.ok)throw new Error("Failed to fetch plugin information");const D=await E.json();D.success&&D.ratings?(T(D.ratings),f(D.totalRatings),S(D.averageRating||0)):(T([]),f(0),S(0))}catch(a){console.error("Error fetching ratings data:",a),T([]),f(0),S(0)}finally{O(!1)}},se=async()=>{if(r)try{Y(!0);const a=localStorage.getItem("adminToken"),E=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${r.slug}?days=15`,{headers:{Authorization:`Bearer ${a}`}});if(!E.ok)throw new Error("Failed to fetch rank history");const D=await E.json();if(D.success&&D.rankHistory){const W=D.rankHistory.map(J=>({date:J.date,rank:J.rank,fetchedAt:J.fetchedAt}));p(W)}else p([])}catch(a){console.error("Error fetching rank history:",a),p([])}finally{Y(!1)}},ne=async()=>{if(r)try{Q(!0);const a=localStorage.getItem("adminToken"),E=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${r.slug}`,{headers:{Authorization:`Bearer ${a}`}});if(!E.ok)throw new Error("Failed to fetch plugin versions");const D=await E.json();D.success?(b(D.versions||{}),A(D.currentVersion),L(D.oldVersions||{})):(b({}),A(null),L({}))}catch(a){console.error("Error fetching versions data:",a),b({}),A(null),L({})}finally{Q(!1)}};n.useEffect(()=>{s&&r&&(ye(),ue(),se(),ne())},[s,r]),n.useEffect(()=>{const a=k=>{i.current&&!i.current.contains(k.target)&&(C(!1),g(""))};if(v)return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[v]);const xe=a=>{var k;return a?((k=a.split(/[-–:]|&#8211;/)[0])==null?void 0:k.trim())||a:""},ie=()=>!M||Object.keys(M).length===0?[]:Object.keys(M).filter(a=>a.toLowerCase()==="trunk"||a===$?!1:h.trim()?a.toLowerCase().includes(h.toLowerCase()):!0).sort((a,k)=>{const E=J=>J.split(".").map(X=>parseInt(X)||0),D=E(a),W=E(k);for(let J=0;J<Math.max(D.length,W.length);J++){const X=(W[J]||0)-(D[J]||0);if(X!==0)return X}return 0}),pe=a=>{P(a),C(!1),g("")},t=()=>{C(!v),v||g("")},u=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],x=a=>{const k=Math.PI/180,{cx:E,cy:D,midAngle:W,innerRadius:J,outerRadius:X,startAngle:B,endAngle:Le,fill:le,payload:_e,percent:We,value:Je}=a,Ue=Math.sin(-k*W),ce=Math.cos(-k*W),Fe=E+(X+10)*ce,Ye=D+(X+10)*Ue,Be=E+(X+30)*ce,Me=D+(X+30)*Ue,je=Be+(ce>=0?1:-1)*22,be=Me,o=ce>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:E,y:D,dy:8,textAnchor:"middle",fill:le,children:[_e.stars,"★"]}),e.jsx(as,{cx:E,cy:D,innerRadius:J,outerRadius:X,startAngle:B,endAngle:Le,fill:le}),e.jsx(as,{cx:E,cy:D,startAngle:B,endAngle:Le,innerRadius:X+6,outerRadius:X+10,fill:le}),e.jsx("path",{d:`M${Fe},${Ye}L${Be},${Me}L${je},${be}`,stroke:le,fill:"none"}),e.jsx("circle",{cx:je,cy:be,r:2,fill:le,stroke:"none"}),e.jsx("text",{x:je+(ce>=0?1:-1)*12,y:be,textAnchor:o,fill:"#333",children:`${Je} ratings`}),e.jsx("text",{x:je+(ce>=0?1:-1)*12,y:be,dy:18,textAnchor:o,fill:"#999",children:`(${(We*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Bs,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[xe((r==null?void 0:r.displayName)||(r==null?void 0:r.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>y(`/plugin-details/${r==null?void 0:r.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(ss,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(Ve,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(we,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),U?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):m.length>0?e.jsx(Se,{width:"100%",height:300,children:e.jsxs(us,{data:m,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Te,{}),e.jsx(Ae,{formatter:a=>[a.toLocaleString(),"Downloads"],labelFormatter:a=>`Date: ${a}`}),e.jsx(xs,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(Ee,{dataKey:"downloads",position:"top",fontSize:10,formatter:a=>a.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(we,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),H?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):N.length>0?e.jsx(Se,{width:"100%",height:300,children:e.jsxs(Ze,{data:N,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Te,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:a=>`#${a}`}),e.jsx(Ae,{formatter:a=>[`#${a}`,"Rank"],labelFormatter:a=>`Date: ${a}`}),e.jsx(es,{type:"monotone",dataKey:"rank",stroke:N.length>1&&N[N.length-1].rank<N[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:N.length>1&&N[N.length-1].rank<N[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(Ee,{dataKey:"rank",position:"top",formatter:a=>`#${a}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(He,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[F?(F/20).toFixed(1):"N/A"," ⭐"]})})]}),I?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):w.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Se,{width:200,height:200,children:e.jsxs(Gs,{children:[e.jsx(Xs,{activeIndex:ae,activeShape:x,data:[...w].sort((a,k)=>k.stars-a.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:re,children:w.map((a,k)=>e.jsx(Qs,{fill:u[k%u.length]},`cell-${k}`))}),e.jsx(Ae,{formatter:(a,k,E)=>[`${a} ratings`,`${E.payload.stars} Star${E.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...w].sort((a,k)=>k.stars-a.stars).map((a,k)=>{const E=w.length>0?a.value/w.reduce((D,W)=>D+W.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:a.stars}),e.jsx(He,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${E}%`,backgroundColor:u[k%u.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:a.value})]},a.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:j})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ss,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),G?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(c).length>0?e.jsxs("div",{className:"space-y-4",children:[$&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(ts,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",$]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:c[$],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(de,{className:"h-4 w-4"})})]})}),Object.keys(M).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(ts,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:i,children:[e.jsxs("button",{type:"button",onClick:t,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:R?"text-gray-900":"text-gray-500",children:R||"Select a version"}),v?e.jsx(Ms,{className:"h-4 w-4 text-gray-400"}):e.jsx(Is,{className:"h-4 w-4 text-gray-400"})]}),v&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(ke,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:h,onChange:a=>g(a.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:ie().length>0?ie().map(a=>e.jsx("button",{type:"button",onClick:()=>pe(a),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:a},a)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:h.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:R?M[R]:"#",target:R?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${R?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:R?"Download Selected Version":"Select a version first",onClick:R?void 0:a=>a.preventDefault(),children:e.jsx(de,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},pt=s=>{if(!s||typeof s!="string")return!1;const l=s.split(".");if(l.length!==3)return!1;try{return l.forEach(r=>{if(r.length===0)throw new Error("Empty JWT part");atob(r.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},Ne=()=>{const s=localStorage.getItem("adminToken");return s?pt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},ns=(s,l)=>{var r;((l==null?void 0:l.status)===401||(r=s==null?void 0:s.message)!=null&&r.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},ft=({plugin:s,onRemove:l,onRefresh:r,canAddPlugins:y})=>{var f,F;const[i,m]=n.useState(!1),[d,w]=n.useState(!1),T=S=>{if(!S)return{formatted:"N/A",daysDiff:"N/A"};try{const N=S.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!N)return{formatted:"N/A",daysDiff:"N/A"};const[,p,c,b]=N,$=`${b}-${c}-${p}`,A=new Date(`${p}-${c}-${b}`),M=new Date;if(isNaN(A.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const L=M-A,R=Math.floor(L/(1e3*60*60*24));return{formatted:$,daysDiff:R}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},j=async()=>{if(r){w(!0);try{await r(s.slug)}finally{w(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:S=>{S.target.style.display="none",S.target.nextSibling.style.display="flex"}}):null,e.jsx(cs,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:j,disabled:d,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(ee,{className:`h-4 w-4 ${d?"animate-spin":""}`})}),y&&e.jsx("button",{onClick:()=>l(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx(De,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((f=s.rankHistory)==null?void 0:f.rankChange)!==null&&((F=s.rankHistory)==null?void 0:F.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const S=s.lastReleaseDate||s.lastFetched,N=T(S);return N.daysDiff==="N/A"||N.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const S=s.lastReleaseDate||s.lastFetched,N=T(S);return e.jsxs(e.Fragment,{children:[N.formatted,N.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",N.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>m(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(Os,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(gt,{isOpen:i,onClose:()=>m(!1),plugin:s})]})},yt=()=>{var ne,xe,ie,pe;const{user:s,autoLogout:l}=fe(),[r,y]=n.useState(!1),[i,m]=n.useState(""),[d,w]=n.useState(!1),[T,j]=n.useState(!1),[f,F]=n.useState(null),[S,N]=n.useState([]),[p,c]=n.useState(null),[b,$]=n.useState(!1),[A,M]=n.useState(!1),[L,R]=n.useState(null),[P,h]=n.useState(!1),[g,v]=n.useState(!1),[C,U]=n.useState(null),_=s&&["admin","superadmin"].includes(s.role),I=async()=>{try{const t=Ne();if(!t){console.warn("No valid authentication token found for database check"),l("No valid authentication token found. Please login again.");return}const x=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(x.ok){const a=await x.json();a.success&&j(a.hasPlugins)}else if(x.status===401){console.warn("Authentication failed during database check");let a="Your session has expired. Please login again.";try{const k=await x.json();k.message&&(a=k.message)}catch{}l(a)}else ns(null,x)}catch(t){if(console.error("Error checking database status:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",t.message),l(t.message);return}ns(t)}},O=async()=>{try{w(!0),F({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const t=Ne();if(!t){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const x=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!x.ok){if(x.status===401){console.warn("Authentication failed during plugin fetch");let E="Your session has expired. Please login again.";try{const D=await x.json();D.message&&(E=D.message)}catch{}l(E);return}throw new Error(`HTTP error! status: ${x.status}`)}const a=x.body.getReader(),k=new TextDecoder;for(;;){const{done:E,value:D}=await a.read();if(E)break;const J=k.decode(D).split(`
`).filter(X=>X.trim());for(const X of J)try{const B=JSON.parse(X);if(B.type==="progress")F({current:B.current||0,total:B.total||0,page:B.page||0,totalPages:B.totalPages||0,successCount:B.successCount||0,errorCount:B.errorCount||0,percentComplete:B.percentComplete||0,estimatedTimeRemaining:B.estimatedTimeRemaining,averageTimePerPage:B.averageTimePerPage,pluginsPerSecond:B.pluginsPerSecond||0,message:B.message||"Processing..."});else if(B.type==="complete")F({current:B.summary.totalProcessedPlugins,total:B.summary.totalPlugins,page:B.summary.totalPages,totalPages:B.summary.totalPages,successCount:B.summary.successfulPages,errorCount:B.summary.failedPages,percentComplete:100,averageTimePerPage:B.summary.averageTimePerPage,pluginsPerSecond:B.summary.averagePluginsPerSecond,successRate:B.summary.successRate,totalDuration:B.summary.totalDuration,message:`✅ Fetch completed! ${B.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(B.summary.totalDuration/1e3/60)} minutes`}),B.errors&&B.errors.length>0&&console.warn("Some errors occurred during fetch:",B.errors),window.toast(`Successfully fetched ${B.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(B.type==="error")throw new Error(B.message||"Fetch failed")}catch(B){console.warn("Failed to parse streaming data:",B)}}}catch(t){if(console.error("Fetch all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",t.message),l(t.message);return}F({current:0,total:0,message:`❌ Full fetch failed: ${t.message}`,error:!0}),window.toast(`Fetch failed: ${t.message}`,"error")}finally{w(!1),setTimeout(()=>F(null),1e4),I()}},H=async()=>{try{const t=await ys("/api/plugins/added");if(!t.ok){let x=`HTTP error! status: ${t.status}`;try{const a=await t.json();a.message&&(x=a.message)}catch{}throw new Error(x)}const u=await t.json();if(u.success){const x=u.addedPlugins.map(a=>{var k;return{slug:a.pluginSlug,name:a.pluginName,displayName:a.displayName,currentRank:a.currentRank,rankGrowth:((k=a.rankHistory)==null?void 0:k.rankChange)||0,lastFetched:a.lastUpdated,short_description:a.short_description,version:a.version,lastReleaseDate:a.lastReleaseDate,icons:a.icons||{},rating:a.rating,numRatings:a.numRatings||0,currentVersion:a.currentVersion,previousVersions:a.previousVersions||[],rankHistory:a.rankHistory,downloadTrend:a.downloadTrend,downloadDataHistory:a.downloadDataHistory||[],reviewStats:a.reviewStats,versionInfo:a.versionInfo,pluginInformation:a.pluginInformation}});N(x)}else console.warn("Failed to load added plugins:",u.message)}catch(t){console.error("Error loading added plugins:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),N([])):(console.warn("Failed to load added plugins:",t.message),N([]))}},Y=t=>{try{localStorage.setItem("pluginData",JSON.stringify(t))}catch(u){console.error("Error storing plugin data in localStorage:",u)}},G=()=>{try{const t=localStorage.getItem("pluginData");return t?JSON.parse(t):null}catch(t){return console.error("Error retrieving plugin data from localStorage:",t),null}},Q=()=>{localStorage.removeItem("pluginData")},ae=async()=>{if(!i.trim()){window.toast("Please enter a plugin slug","warning");return}try{$(!0);const t=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${i.trim()}&request[fields][icons]=true`,u=await fetch(t);if(!u.ok)throw new Error(`WordPress API error: ${u.status}`);const x=await u.json();if(x.error){window.toast(`Plugin not found: ${x.error}`,"error"),c(null);return}const a={slug:x.slug,name:x.name,version:x.version,author:x.author,rating:x.rating,active_installs:x.active_installs,num_ratings:x.num_ratings,downloaded:x.downloaded,last_updated:x.last_updated,short_description:x.short_description,homepage:x.homepage,requires:x.requires,tested:x.tested,requires_php:x.requires_php,icons:x.icons?{"1x":x.icons["1x"],"2x":x.icons["2x"]}:{},tags:x.tags?Object.keys(x.tags).slice(0,10):[]};Y(a),c({slug:x.slug,name:x.name,version:x.version,author:x.author,rating:x.rating,active_installs:x.active_installs,num_ratings:x.num_ratings,downloaded:x.downloaded,last_updated:x.last_updated,homepage:x.homepage,requires:x.requires,tested:x.tested,requires_php:x.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(t){console.error("Error fetching plugin data:",t),window.toast("Failed to fetch plugin data from WordPress API","error"),c(null)}finally{$(!1)}};n.useEffect(()=>{H(),I()},[]);const me=async()=>{const t=G();if(!t){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!i.trim()){window.toast("Please enter a plugin slug","warning");return}h(!0);try{const u=Ne();if(!u){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const a=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify({slug:i.trim(),pluginData:t})});if(!a.ok){if(a.status===401){console.warn("Authentication failed during plugin addition");let E="Your session has expired. Please login again.";try{const D=await a.json();D.message&&(E=D.message)}catch{}l(E);return}else if(a.status===413){console.warn("Payload too large error during plugin addition");try{const E=await a.json();window.toast(E.message||"Plugin data is too large. Please try again or contact support.","error")}catch{window.toast("Plugin data is too large. Please try again or contact support.","error")}return}}const k=await a.json();k.success?(window.toast(k.message,"success"),Q(),m(""),y(!1),c(null),await H()):window.toast(k.message||"Failed to add plugin","error")}catch(u){if(console.error("Add plugin error:",u),u.message&&(u.message.includes("expired")||u.message.includes("token")||u.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",u.message),l(u.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{h(!1)}},re=t=>{const u=S.find(x=>x.slug===t)||addedPluginsListData.find(x=>x.slug===t);R(u),M(!0)},ye=async()=>{if(L)try{const t=Ne();if(!t){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const x=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${L.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!x.ok&&x.status===401){console.warn("Authentication failed during plugin removal");let k="Your session has expired. Please login again.";try{const E=await x.json();E.message&&(k=E.message)}catch{}l(k);return}const a=await x.json();a.success?(window.toast("Plugin removed successfully","success"),await H()):window.toast(a.message||"Failed to remove plugin","error")}catch(t){if(console.error("Remove plugin error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",t.message),l(t.message);return}window.toast("Failed to remove plugin","error")}finally{M(!1),R(null)}},ue=async t=>{try{const u=Ne();if(!u){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const a=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${t}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"}});if(!a.ok&&a.status===401){console.warn("Authentication failed during plugin refresh");let E="Your session has expired. Please login again.";try{const D=await a.json();D.message&&(E=D.message)}catch{}l(E);return}const k=await a.json();k.success?(window.toast(k.message,"success"),await H()):window.toast(k.message||"Failed to refresh plugin","error")}catch(u){if(console.error("Refresh plugin error:",u),u.message&&(u.message.includes("expired")||u.message.includes("token")||u.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",u.message),l(u.message);return}window.toast("Failed to refresh plugin","error")}},se=async()=>{try{v(!0),U({current:0,total:0,currentPlugin:null});const t=Ne();if(!t){console.error("No valid authentication token found"),l("No valid authentication token found. Please login again.");return}const u="https://pluginsight.vercel.app",x=await fetch(`${u}/api/plugins/added/slugs`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!x.ok){if(x.status===401){console.warn("Authentication failed during plugin slugs fetch");let E="Your session has expired. Please login again.";try{const D=await x.json();D.message&&(E=D.message)}catch{}l(E);return}throw new Error(`Failed to fetch plugin slugs: ${x.status}`)}const a=await x.json();if(!a.success||!a.slugs||a.slugs.length===0){window.toast("No plugins found to refresh","warning");return}const k=a.slugs;U({current:0,total:k.length,currentPlugin:null}),window.toast(`Starting refresh of ${k.length} plugins...`,"info");for(let E=0;E<k.length;E++){const D=k[E];U({current:E+1,total:k.length,currentPlugin:D});try{const W=await fetch(`${u}/api/plugins/added/${D}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!W.ok&&W.status===401){console.warn("Authentication failed during plugin refresh");let X="Your session has expired. Please login again.";try{const B=await W.json();B.message&&(X=B.message)}catch{}l(X);return}const J=await W.json();J.success?console.log(`Successfully refreshed plugin: ${D}`):console.warn(`Failed to refresh plugin ${D}: ${J.message}`)}catch(W){console.error(`Error refreshing plugin ${D}:`,W)}E<k.length-1&&await new Promise(W=>setTimeout(W,6e4))}window.toast("All plugins refreshed successfully!","success"),await H()}catch(t){if(console.error("Refresh all plugins error:",t),t.message&&(t.message.includes("expired")||t.message.includes("token")||t.message.includes("Authentication failed"))){console.warn("JWT token error detected during refresh all:",t.message),l(t.message);return}window.toast("Failed to refresh all plugins","error")}finally{v(!1),U(null)}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[_&&e.jsx("button",{onClick:O,disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"h-4 w-4 mr-2"}),T?"Refetch":"Fetch"]})}),_&&e.jsxs("button",{onClick:()=>y(!0),disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(ve,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),f&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${f.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${f.error?"text-red-900":"text-blue-900"}`,children:f.message}),f.total>0&&e.jsxs("span",{className:`text-sm ${f.error?"text-red-700":"text-blue-700"}`,children:[(ne=f.current)==null?void 0:ne.toLocaleString(),"/",(xe=f.total)==null?void 0:xe.toLocaleString()]})]}),f.page&&f.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",f.page,"/",f.totalPages]}),e.jsxs("div",{children:["Progress: ",f.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(ie=f.successCount)==null?void 0:ie.toLocaleString()," success"]}),f.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(pe=f.errorCount)==null?void 0:pe.toLocaleString()," errors"]})]})]}),(f.pluginsPerSecond>0||f.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[f.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",f.pluginsPerSecond," plugins/sec"]}),f.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(f.estimatedTimeRemaining/1e3/60)," ","min"]}),f.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(f.averageTimePerPage/1e3),"s/page"]}),f.successRate&&e.jsxs("div",{children:["Success Rate: ",f.successRate,"%"]})]}),f.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(f.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",f.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",f.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",f.successCount,"/",f.totalPages]})]})}),f.total>0&&!f.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,f.percentComplete||f.current/f.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[f.percentComplete||Math.round(f.current/f.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-900",children:["Added Plugins (",S.length,")"]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[_&&S.length>0&&e.jsx("button",{onClick:se,disabled:g,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${g?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white text-sm`,title:"Refresh all plugins sequentially with 60-second delays",children:g?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Refreshing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Refresh All"]})}),e.jsx("div",{className:"text-sm text-gray-500",children:S.length>0?`Showing ${S.length} plugins`:"No plugins added yet"})]})]}),C&&e.jsxs("div",{className:"mb-6 p-4 rounded-lg border bg-blue-50 border-blue-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Refreshing plugins..."}),e.jsxs("span",{className:"text-sm text-blue-700",children:[C.current,"/",C.total]})]}),C.currentPlugin&&e.jsxs("div",{className:"text-sm text-blue-700 mb-2",children:["Currently refreshing:"," ",e.jsx("span",{className:"font-mono",children:C.currentPlugin})]}),e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.max(2,C.current/C.total*100)}%`}})}),e.jsx("div",{className:"text-xs text-blue-600 mt-2",children:"Note: Each plugin refresh has a 60-second delay to avoid overwhelming the backend."})]}),S.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:S.map(t=>e.jsx(ft,{plugin:t,onRemove:re,onRefresh:ue,canAddPlugins:_},t.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(cs,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(ge,{isOpen:r,onClose:()=>{P||(y(!1),c(null),m(""),Q())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:i,onChange:t=>m(t.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:ae,disabled:b||!i.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),p&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:p.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:p.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const t=p.author;if(!t)return"N/A";const u=t.match(/<a[^>]*>(.*?)<\/a>/);return u?u[1]:t})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:p.rating?`${p.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:p.active_installs?p.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:p.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",p.requires||"N/A"," | Tested up to"," ",p.tested||"N/A"," | PHP"," ",p.requires_php||"N/A"]})]})]})]}),!p&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{y(!1),c(null),m(""),Q()},disabled:P,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:me,disabled:P||!p,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:P?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(ge,{isOpen:A,onClose:()=>{M(!1),R(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(De,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(L==null?void 0:L.displayName)||(L==null?void 0:L.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{M(!1),R(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:ye,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx(De,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},bt=()=>{var L,R,P;const[s,l]=n.useState([]),[r,y]=n.useState(""),[i,m]=n.useState("7"),[d,w]=n.useState({start:"",end:""}),[T,j]=n.useState([]),[f,F]=n.useState(!1),[S,N]=n.useState(!1);console.log("Chart data: ",T);const p=h=>{var H;const{cx:g,cy:v,payload:C}=h;if(!r||!C)return null;const U=((H=s.find(Y=>Y.pluginSlug===r))==null?void 0:H.displayName)||r,_=C[`${U}_trend`],I=C[U];if(I==null)return null;let O="#3B82F6";return _==="improvement"?O="#10B981":_==="decline"&&(O="#EF4444"),e.jsx("circle",{cx:g,cy:v,r:5,fill:O,stroke:O,strokeWidth:2})},c=async()=>{var h;try{const g=localStorage.getItem("adminToken"),C=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!C.ok)throw new Error(`HTTP error! status: ${C.status}`);const U=await C.json();U.success?(l(U.plugins),console.log(`✅ Loaded ${((h=U.plugins)==null?void 0:h.length)||0} plugins from plugininformations collection (${U.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",U.message)}catch(g){console.error("Error loading plugins from plugininformations collection:",g),g.name==="TypeError"&&g.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},b=async()=>{if(!r){j([]);return}try{F(!0);const h=s.find(v=>v.pluginSlug===r);if(!h){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),j([]);return}if(!h.rankHistory||!Array.isArray(h.rankHistory)){console.log(`Plugin ${r} has no rank history data`),j([]);return}console.log(`📊 Processing rank history for ${r}: ${h.rankHistory.length} entries`);const g=$(h);j(g)}catch(h){console.error("Error loading chart data:",h),window.toast("Failed to load chart data","error")}finally{F(!1)}},$=h=>{if(!h.rankHistory||!Array.isArray(h.rankHistory))return[];const g=h.displayName||h.pluginName||h.pluginSlug;let v=h.rankHistory;if(i!=="custom"){const _=parseInt(i),I=new Date;I.setDate(I.getDate()-_),v=h.rankHistory.filter(O=>{const[H,Y,G]=O.date.split("-");return new Date(G,Y-1,H)>=I})}else if(d.start&&d.end){const _=new Date(d.start),I=new Date(d.end);v=h.rankHistory.filter(O=>{const[H,Y,G]=O.date.split("-"),Q=new Date(G,Y-1,H);return Q>=_&&Q<=I})}return v.sort((_,I)=>{const[O,H,Y]=_.date.split("-"),[G,Q,ae]=I.date.split("-"),me=new Date(Y,H-1,O),re=new Date(ae,Q-1,G);return me-re}).map((_,I,O)=>{const H=O[I-1];let Y="stable";return H&&(_.previousRank<H.previousRank?Y="improvement":_.previousRank>H.previousRank&&(Y="decline")),{date:_.date,[g]:_.previousRank,[`${g}_trend`]:Y}})},A=async()=>{if(!r){window.toast("Please select a plugin first","warning");return}try{F(!0),window.toast("Refreshing chart data...","info"),await b(),window.toast("Chart data refreshed successfully","success")}catch(h){console.error("Error refreshing chart data:",h),window.toast("Failed to refresh chart data","error")}finally{F(!1)}};n.useEffect(()=>{c()},[]),n.useEffect(()=>{b()},[r,i,d]);const M=h=>{m(h),N(h==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(we,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:r,onChange:h=>y(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(h=>e.jsx("option",{value:h.pluginSlug,children:h.displayName},h.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:i,onChange:h=>M(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),S&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:d.start,onChange:h=>w(g=>({...g,start:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:d.end,onChange:h=>w(g=>({...g,end:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:A,disabled:f||!r,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(ee,{className:`h-4 w-4 mr-2 ${f?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",r&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((L=s.find(h=>h.pluginSlug===r))==null?void 0:L.displayName)||r,")"]})]}),f?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):T.length>0?e.jsx("div",{className:"h-96",children:e.jsx(Se,{width:"100%",height:"100%",children:e.jsxs(Ze,{data:T,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Te,{tick:{fontSize:12},domain:(()=>{var Y;if(T.length===0)return[1,100];const h=((Y=s.find(G=>G.pluginSlug===r))==null?void 0:Y.displayName)||r,g=T.map(G=>G[h]).filter(G=>G!=null);if(g.length===0)return[1,100];const v=Math.min(...g),C=Math.max(...g),U=g[g.length-1],_=Math.max(1,U-10),I=U+10,O=Math.min(_,v-2),H=Math.max(I,C+2);return[O,H]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(Ae,{labelFormatter:h=>`Date: ${h}`,formatter:(h,g)=>[h?`#${h}`:"No data",g]}),e.jsx(Zs,{}),r&&e.jsx(es,{type:"monotone",dataKey:((R=s.find(h=>h.pluginSlug===r))==null?void 0:R.displayName)||r,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(p,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(Ee,{dataKey:((P=s.find(h=>h.pluginSlug===r))==null?void 0:P.displayName)||r,position:"top",fontSize:10,fill:"#374151",formatter:h=>h?`#${h}`:""})},r)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(we,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:r?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},ws=s=>{const l=document.createElement("textarea");return l.innerHTML=s,l.value},wt=(s,l=40)=>{const r=ws(s);if(r.length<=l)return r;const y=r.split(" ");let i=y[0];for(let m=1;m<y.length&&(i+" "+y[m]).length<=l-3;m++)i+=" "+y[m];return i+"..."},jt=()=>{var be;const[s,l]=n.useState("performance"),[r,y]=n.useState([]),[i,m]=n.useState(""),[d,w]=n.useState([]),[T,j]=n.useState(!1),[f,F]=n.useState(!1),[S,N]=n.useState(""),[p,c]=n.useState(""),[b,$]=n.useState(!1),[A,M]=n.useState(new Set),[L,R]=n.useState(!1),[P,h]=n.useState(new Set),[g,v]=n.useState(null),[C,U]=n.useState(!1),[_,I]=n.useState([]),[O,H]=n.useState(!1),[Y,G]=n.useState(!1),[Q,ae]=n.useState(""),[me,re]=n.useState({}),[ye,ue]=n.useState(!1),[se,ne]=n.useState(10),[xe,ie]=n.useState(!1),[pe,t]=n.useState(!1),[u,x]=n.useState(null),[a,k]=n.useState([]),[E,D]=n.useState([]),[W,J]=n.useState(!1),[X,B]=n.useState(!1),Le=async()=>{var o;try{const z=localStorage.getItem("adminToken"),q=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();q.success?(y(q.plugins),console.log(`✅ Loaded ${((o=q.plugins)==null?void 0:o.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",q.message)}catch(z){console.error("Error loading plugins from plugininformations collection:",z),window.toast("Failed to load plugins from database","error")}},le=async()=>{try{if(j(!0),!i){w([]),re({}),j(!1);return}const o=localStorage.getItem("adminToken"),V=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${i}`,q=await(await fetch(V,{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();if(q.success){const te=q.keywords.map(he=>({...he,position:he.latestRank,lastChecked:he.lastChecked||he.updatedAt}));w(te);const Z={};te.forEach(he=>{Z[he._id]=he.occurrences||0}),re(Z),ne(10)}else console.error("Failed to load keywords:",q.message),window.toast(q.message||"Failed to load keywords","error"),w([]),re({})}catch(o){console.error("Error loading keywords:",o),window.toast("Failed to load keywords","error"),w([]),re({})}finally{j(!1)}},_e=async()=>{if(!p||!S.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const o=localStorage.getItem("adminToken"),z=r.find(te=>te.pluginSlug===p),q=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:p,pluginName:(z==null?void 0:z.displayName)||p,keyword:S.trim()})})).json();q.success?(window.toast("Keyword added successfully","success"),N(""),c(""),F(!1),p===i&&le()):window.toast(q.message||"Failed to add keyword","error")}catch(o){console.error("Error adding keyword:",o),window.toast("Failed to add keyword","error")}},We=async()=>{try{$(!0),window.toast("Refreshing keyword ranks...","info");const o=localStorage.getItem("adminToken"),K=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();K.success?(window.toast(K.message,"success"),await le()):window.toast(K.message||"Failed to refresh keyword ranks","error")}catch(o){console.error("Error refreshing keyword ranks:",o),window.toast("Failed to refresh keyword ranks","error")}finally{$(!1)}},Je=async()=>{try{const o=localStorage.getItem("adminToken"),z="https://pluginsight.vercel.app",V=Array.from(A),q=await(await fetch(`${z}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:V})})).json();q.success?(window.toast(`${V.length} keywords deleted successfully`,"success"),M(new Set),R(!1),le()):window.toast(q.message||"Failed to delete keywords","error")}catch(o){console.error("Error deleting keywords:",o),window.toast("Failed to delete keywords","error")}},Ue=async()=>{if(g)try{const o=localStorage.getItem("adminToken"),K=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${g}`,{method:"DELETE",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();if(K.success){window.toast("Keyword deleted successfully","success"),U(!1),v(null);const q=new Set(P);q.delete(g),h(q),le()}else window.toast(K.message||"Failed to delete keyword","error")}catch(o){console.error("Error deleting keyword:",o),window.toast("Failed to delete keyword","error")}},ce=async(o=!1)=>{try{H(!0);const z=localStorage.getItem("adminToken"),te=await(await fetch(`https://pluginsight.vercel.app/api/competitors${o?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();te.success&&(I(te.competitors),o&&te.competitors.length>0&&window.toast(`Discovered ${te.competitors.length} competitor plugins`,"success"))}catch(z){console.error("Error loading competitors:",z),window.toast("Failed to load competitors","error")}finally{H(!1)}},Fe=async()=>{if(!Q.trim()){window.toast("Please enter a plugin slug","error");return}try{const o=localStorage.getItem("adminToken"),K=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:Q.trim()})})).json();K.success?(window.toast("Competitor added successfully","success"),ae(""),G(!1),ce()):window.toast(K.message||"Failed to add competitor","error")}catch(o){console.error("Error adding competitor:",o),window.toast("Failed to add competitor","error")}},Ye=async o=>{x(o),ue(!0),J(!0);try{const z=localStorage.getItem("adminToken"),q=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(o.keyword)}/${o.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();q.success?k(q.rankHistory):(window.toast("Failed to load rank history","error"),k([]))}catch(z){console.error("Error loading rank history:",z),window.toast("Failed to load rank history","error"),k([])}finally{J(!1)}},Be=async o=>{x(o),t(!0),B(!0);try{const V=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(o.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,q=await(await fetch(V)).json();if(q&&q.plugins){const te=q.plugins.filter(Z=>Z.slug!==o.pluginSlug).sort((Z,he)=>(he.active_installs||0)-(Z.active_installs||0)).slice(0,10).map(Z=>({pluginName:Z.name,pluginSlug:Z.slug,activeInstalls:Z.active_installs||0,rating:Z.rating||0,numRatings:Z.num_ratings||0,testedUpTo:Z.tested||"N/A",lastUpdated:Z.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${Z.slug}/`}));D(te)}else window.toast("Failed to load related plugins","error"),D([])}catch(z){console.error("Error loading related plugins:",z),window.toast("Failed to load related plugins","error"),D([])}finally{B(!1)}};n.useEffect(()=>{Le()},[]),n.useEffect(()=>{le()},[i]);const Me=()=>{se<d.length&&!xe&&(ie(!0),setTimeout(()=>{ne(o=>Math.min(o+10,d.length)),ie(!1)},500))},je=o=>{const{scrollTop:z,scrollHeight:V,clientHeight:K}=o.target;V-z<=K+100&&Me()};return n.useEffect(()=>{s==="competitors"&&ce(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ke,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>l("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(we,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>l("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(Ce,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:i,onChange:o=>m(o.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),r.map(o=>e.jsx("option",{value:o.pluginSlug,children:o.displayName},o.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[A.size>0&&e.jsxs("button",{onClick:()=>R(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx(De,{className:"h-4 w-4 mr-1"}),"Delete (",A.size,")"]}),e.jsxs("button",{onClick:()=>F(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(ve,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:We,disabled:b,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(ee,{className:`h-4 w-4 mr-1 ${b?"animate-spin":""}`}),b?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",i&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(be=r.find(o=>o.pluginSlug===i))==null?void 0:be.displayName]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",Math.min(se,d.length)," of"," ",d.length," keywords"]}),se<d.length&&e.jsx("div",{className:"text-sm text-blue-600",children:"Scroll down to load more..."})]})]}),T?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):d.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",onScroll:je,children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:d.slice(0,se).every(o=>A.has(o._id))&&d.length>0,onChange:o=>{const z=d.slice(0,se),V=new Set(A);o.target.checked?z.forEach(K=>V.add(K._id)):z.forEach(K=>V.delete(K._id)),M(V)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.slice(0,se).map((o,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:A.has(o._id),onChange:V=>{const K=new Set(A);V.target.checked?K.add(o._id):K.delete(o._id),M(K)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.keyword})}),o.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${o.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:o.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:o.position||"-"}),o.rankChange!==null&&o.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${o.rankChange<0?"bg-green-100 text-green-800":o.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[o.rankChange<0?"↑":o.rankChange>0?"↓":"=",Math.abs(o.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ye(o),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(we,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>Be(o),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Hs,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:me[o._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:o.addedAt?new Date(o.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:(()=>{const V=o.lastChecked||o.updatedAt;if(!V)return"-";const K=new Date(V);return isNaN(K.getTime())?"-":K.toLocaleDateString("en-GB")})()})]},o._id))})]})}),xe&&e.jsxs("div",{className:"p-4 text-center",children:[e.jsx(ee,{className:"h-5 w-5 text-blue-600 animate-spin mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Loading more keywords..."})]}),se>=d.length&&d.length>10&&e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-gray-500",children:"All keywords loaded"})})]}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ke,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:i?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),i&&e.jsxs("button",{onClick:()=>F(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(ve,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>ce(!0),disabled:O,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(ee,{className:`h-4 w-4 mr-1 ${O?"animate-spin":""}`}),O?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>G(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(ve,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),O?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):_.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:_.map((o,z)=>{var V;return e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:o.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.currentRank?`#${o.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((V=o.activeInstalls)==null?void 0:V.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[o.tags&&o.tags.length>0?o.tags.slice(0,3).map((K,q)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:K},`${o._id}-tag-${q}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),o.tags&&o.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",o.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(o.createdAt).toLocaleDateString("en-GB")})]},o._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>G(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(ve,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(ge,{isOpen:f,onClose:()=>{F(!1),N(""),c("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:p,onChange:o=>c(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),r.map(o=>e.jsx("option",{value:o.pluginSlug,children:o.displayName},o.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:S,onChange:o=>N(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:o=>{o.key==="Enter"&&_e()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{F(!1),N(""),c("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:_e,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(ge,{isOpen:L,onClose:()=>R(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",A.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>R(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Je,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(ge,{isOpen:C,onClose:()=>{U(!1),v(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{U(!1),v(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Ue,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(ge,{isOpen:Y,onClose:()=>{G(!1),ae("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:Q,onChange:o=>ae(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:o=>{o.key==="Enter"&&Fe()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{G(!1),ae("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Fe,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(ge,{isOpen:ye,onClose:()=>{ue(!1),x(null),k([])},title:`Rank Analytics - ${(u==null?void 0:u.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[W?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):a.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",u==null?void 0:u.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(Se,{width:"100%",height:"100%",children:e.jsxs(Ze,{data:a.slice().reverse().map(o=>({...o,displayDate:o.date,invertedRank:o.rank?-o.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(Te,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:o=>`#${Math.abs(o)}`}),e.jsx(Ae,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:o=>[`#${Math.abs(o)}`,"Rank"],labelFormatter:o=>`Date: ${o}`}),e.jsx(es,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(Ee,{dataKey:"invertedRank",position:"top",formatter:o=>`#${Math.abs(o)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(zs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{ue(!1),x(null),k([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(ge,{isOpen:pe,onClose:()=>{t(!1),x(null),D([])},title:`Related Plugins - "${(u==null?void 0:u.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:X?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):E.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map((o,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:o.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:ws(o.pluginName),children:[wt(o.pluginName),e.jsx(ze,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:o.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.activeInstalls>0?o.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(He,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[o.rating>0?o.rating.toFixed(1):"N/A",o.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",o.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.testedUpTo!=="N/A"?`WP ${o.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.lastUpdated!=="N/A"?(()=>{const V=new Date(o.lastUpdated);if(isNaN(V.getTime()))return"N/A";const K=V.getDate().toString().padStart(2,"0"),q=(V.getMonth()+1).toString().padStart(2,"0"),te=V.getFullYear();return`${q}-${K}-${te}`})():"N/A"})]},o.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ke,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{t(!1),x(null),D([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},Nt=()=>{const[s,l]=n.useState("downloaded-data"),[r,y]=n.useState([]),[i,m]=n.useState(!1),[d,w]=n.useState(""),[T,j]=n.useState([]),[f,F]=n.useState(!1),[S,N]=n.useState("15"),[p,c]=n.useState({start:"",end:""}),[b,$]=n.useState(""),[A,M]=n.useState([]),[L,R]=n.useState(!1),[P,h]=n.useState({startDate:"",endDate:"",rating:[],page:1}),[g,v]=n.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});n.useEffect(()=>{C()},[]);const C=async()=>{try{m(!0);const _=localStorage.getItem("adminToken"),O=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${_}`,"Content-Type":"application/json"}});if(!O.ok)throw new Error(`HTTP error! status: ${O.status}`);const H=await O.json();H.success?y(H.plugins):console.warn("Failed to load added plugins:",H.message)}catch(_){console.error("Error loading added plugins:",_),_.name==="TypeError"&&_.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{m(!1)}},U=[{id:"downloaded-data",name:"Downloaded Data",icon:de},{id:"plugin-reviews",name:`Plugin Reviews${g.totalReviews>0?` (${g.totalReviews})`:""}`,icon:Ge}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(os,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:U.map(_=>{const I=_.icon;return e.jsxs("button",{onClick:()=>l(_.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===_.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(I,{className:"h-4 w-4"}),e.jsx("span",{children:_.name})]},_.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(vt,{addedPlugins:r,selectedPlugin:d,setSelectedPlugin:w,downloadData:T,setDownloadData:j,downloadLoading:f,setDownloadLoading:F,dateRange:S,setDateRange:N,customDateRange:p,setCustomDateRange:c}),s==="plugin-reviews"&&e.jsx(kt,{addedPlugins:r,reviewsPlugin:b,setReviewsPlugin:$,reviews:A,setReviews:M,reviewsLoading:L,setReviewsLoading:R,reviewFilters:P,setReviewFilters:h,reviewStats:g,setReviewStats:v})]})]})]})},vt=({addedPlugins:s,selectedPlugin:l,setSelectedPlugin:r,downloadData:y,setDownloadData:i,downloadLoading:m,setDownloadLoading:d,dateRange:w,setDateRange:T,customDateRange:j,setCustomDateRange:f})=>{var N;const F=async()=>{try{d(!0),window.toast("Starting plugin download data fetch...","info");const p=localStorage.getItem("adminToken"),$=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json"}})).json();$.success?(window.toast($.message,"success"),l&&S(l)):window.toast($.message||"Failed to refresh download data","error")}catch(p){console.error("Error refreshing download data:",p),window.toast("Failed to refresh download data","error")}finally{d(!1)}},S=async p=>{if(p)try{d(!0);const c=localStorage.getItem("adminToken"),b="https://pluginsight.vercel.app";let $=`${b}/api/analytics/download-data/${p}?days=${w}`;w==="custom"&&j.start&&j.end&&($=`${b}/api/analytics/download-data/${p}?startDate=${j.start}&endDate=${j.end}`);const M=await(await fetch($,{headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"}})).json();if(M.success){const L=M.downloadData.map(R=>({date:new Date(R.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:R.downloads,fullDate:R.date}));i(L)}}catch(c){console.error("Error loading download data:",c),window.toast("Failed to load download data","error")}finally{d(!1)}};return Xe.useEffect(()=>{l&&S(l)},[l,w,j]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:l,onChange:p=>r(p.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(p=>e.jsx("option",{value:p.pluginSlug,children:p.displayName||p.pluginName},p.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:w,onChange:p=>T(p.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),w==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:j.start,onChange:p=>f(c=>({...c,start:p.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:j.end,onChange:p=>f(c=>({...c,end:p.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:F,disabled:m,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(ee,{className:`h-4 w-4 mr-2 ${m?"animate-spin":""}`}),"Refresh"]})]}),l&&y.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((N=s.find(p=>p.pluginSlug===l))==null?void 0:N.displayName)||l]}),e.jsx("div",{className:"h-96",children:e.jsx(Se,{width:"100%",height:"100%",children:e.jsxs(us,{data:y,children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(Te,{stroke:"#6b7280",fontSize:12,tickFormatter:p=>p.toLocaleString()}),e.jsx(Ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:p=>[p.toLocaleString(),"Downloads"],labelFormatter:(p,c)=>c&&c[0]?new Date(c[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):p}),e.jsx(xs,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(Ee,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:p=>p.toLocaleString()})})]})})})]}):l&&m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):l?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(de,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(de,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},kt=({addedPlugins:s,reviewsPlugin:l,setReviewsPlugin:r,reviews:y,setReviews:i,reviewsLoading:m,setReviewsLoading:d,reviewFilters:w,setReviewFilters:T,reviewStats:j,setReviewStats:f})=>{const F=async()=>{try{d(!0),window.toast("Starting plugin reviews fetch...","info");const c=localStorage.getItem("adminToken"),$=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"}});if(!$.ok)throw new Error(`HTTP error! status: ${$.status}`);const A=await $.json();A.success?(window.toast(A.message,"success"),l&&S(l)):window.toast(A.message||"Failed to refresh reviews","error")}catch(c){console.error("Error refreshing reviews:",c),c.name==="TypeError"&&c.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{d(!1)}},S=async(c,b=1)=>{var $;if(c)try{d(!0);const A=new URLSearchParams({page:b.toString(),limit:"20"});w.startDate&&A.append("startDate",w.startDate),w.endDate&&A.append("endDate",w.endDate),w.rating.length>0&&w.rating.forEach(R=>A.append("rating",R.toString())),console.log(`Loading reviews for plugin: ${c} with params:`,A.toString());const M=await ys(`/api/analytics/reviews/${c}?${A}`);if(!M.ok)throw new Error(`HTTP error! status: ${M.status}`);const L=await M.json();console.log("Reviews API response:",L),L.success?(console.log(`Loaded ${(($=L.reviews)==null?void 0:$.length)||0} reviews for ${c} (page ${b})`),i(b===1?L.reviews||[]:R=>[...R,...L.reviews||[]]),f(L.stats||{})):(console.warn("Reviews API returned success: false",L),b===1&&i([]),f({}))}catch(A){console.error("Error loading reviews:",A),A.name==="TypeError"&&A.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${A.message}`,"error"),b===1&&(i([]),f({}))}finally{d(!1)}};Xe.useEffect(()=>{l&&S(l)},[l,w]);const N=c=>Array.from({length:5},(b,$)=>e.jsx("span",{className:`text-lg ${$<c?"text-yellow-400":"text-gray-300"}`,children:"★"},$)),p=c=>{if(!c)return"";let b=c.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();b=b.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const $=document.createElement("textarea");return $.innerHTML=b,b=$.value,b=b.replace(/<[^>]*>/g,""),b=b.replace(/\s+/g," ").trim(),b};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:l,onChange:c=>r(c.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(c=>e.jsx("option",{value:c.pluginSlug,children:c.displayName||c.pluginName},c.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:w.startDate,onChange:c=>T(b=>({...b,startDate:c.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:w.endDate,onChange:c=>T(b=>({...b,endDate:c.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:w.rating.length===1?w.rating[0]:"",onChange:c=>{const b=c.target.value;T(b===""?$=>({...$,rating:[]}):$=>({...$,rating:[parseInt(b)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:F,disabled:m,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(ee,{className:`h-4 w-4 mr-2 ${m?"animate-spin":""}`}),"Refresh"]})]}),l&&y.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:j.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:y.map((c,b)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${b*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:N(c.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",c.author," •"," ",new Date(c.date).toLocaleDateString("en-GB")]})]}),c.reviewUrl&&e.jsx("a",{href:c.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(ze,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:c.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:p(c.content)})]},c._id||b))})]}):l&&m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ee,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):l?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ge,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ge,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},St=()=>{const{user:s}=fe(),[l,r]=n.useState([]),[y,i]=n.useState(!0),[m,d]=n.useState(""),[w,T]=n.useState(""),[j,f]=n.useState(""),[F,S]=n.useState(!1),[N,p]=n.useState(!1),[c,b]=n.useState(!1),[$,A]=n.useState(!1),[M,L]=n.useState(!1),[R,P]=n.useState(null),[h,g]=n.useState({current:1,pages:1,total:0,limit:10}),[v,C]=n.useState({name:"",email:"",password:"",role:"member"}),[U,_]=n.useState({newPassword:"",confirmPassword:""}),[I,O]=n.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),H=async(t=1,u="",x="")=>{try{i(!0);const a=localStorage.getItem("adminToken");if(!a){d("Authentication token not found. Please login again.");return}const k=new URLSearchParams({page:t.toString(),limit:"10",...u&&{search:u},...x&&{role:x}}),D=await fetch(`https://pluginsight.vercel.app/api/users?${k}`,{headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!D.ok){if(D.status===401){d("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${D.status}`)}const W=await D.json();W.success?(r(W.users),g(W.pagination),d("")):d(W.message||"Failed to fetch users")}catch(a){console.error("Fetch users error:",a),a.name==="TypeError"&&a.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to fetch users. Please try again.")}finally{i(!1)}};n.useEffect(()=>{H()},[]);const Y=()=>{H(1,w,j)},G=async t=>{t.preventDefault();try{const u=localStorage.getItem("adminToken"),a=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify(v)});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const k=await a.json();k.success?(S(!1),C({name:"",email:"",password:"",role:"member"}),H(h.current,w,j)):d(k.message||"Failed to create user")}catch(u){console.error("Add user error:",u),u.name==="TypeError"&&u.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to create user")}},Q=async t=>{t.preventDefault();try{const u=localStorage.getItem("adminToken"),a=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"PUT",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify({name:v.name,email:v.email,role:v.role,isActive:v.isActive})});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const k=await a.json();k.success?(p(!1),P(null),H(h.current,w,j)):d(k.message||"Failed to update user")}catch(u){console.error("Edit user error:",u),u.name==="TypeError"&&u.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to update user")}},ae=t=>{if(s&&s._id===t._id){window.toast("You cannot delete your own account","error");return}P(t),A(!0)},me=async()=>{if(R)try{const t=localStorage.getItem("adminToken"),x=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!x.ok)throw new Error(`HTTP error! status: ${x.status}`);const a=await x.json();a.success?(H(h.current,w,j),A(!1),P(null)):d(a.message||"Failed to delete user")}catch(t){console.error("Delete user error:",t),t.name==="TypeError"&&t.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to delete user")}},re=async t=>{if(t.preventDefault(),U.newPassword!==U.confirmPassword){d("Passwords do not match");return}try{const u=localStorage.getItem("adminToken"),a=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:U.newPassword})});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const k=await a.json();k.success?(b(!1),P(null),_({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):d(k.message||"Failed to reset password")}catch(u){console.error("Reset password error:",u),u.name==="TypeError"&&u.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to reset password")}},ye=t=>{P(t),C({name:t.name,email:t.email,role:t.role,isActive:t.isActive}),p(!0)},ue=t=>{P(t),_({newPassword:"",confirmPassword:""}),b(!0)},se=t=>{var u,x,a,k;P(t),O({canAddPlugins:((u=t.permissions)==null?void 0:u.canAddPlugins)||!1,canDeletePlugins:((x=t.permissions)==null?void 0:x.canDeletePlugins)||!1,canAddKeywords:((a=t.permissions)==null?void 0:a.canAddKeywords)||!1,canAddUsers:((k=t.permissions)==null?void 0:k.canAddUsers)||!1}),L(!0)},ne=t=>{switch(t){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},xe=t=>s.role==="superadmin"||s.role==="admin"&&t.role==="member"?s._id!==t._id:!1,ie=t=>!(s.role==="admin"&&t.role==="superadmin"),pe=t=>s._id===t._id?!1:s.role==="superadmin"||s.role==="admin"&&t.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Ks,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>S(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(ve,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(ke,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:w,onChange:t=>T(t.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Vs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:j,onChange:t=>f(t.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:Y,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),m&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:m}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:y?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):l.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:l.filter(ie).map(t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.name}),e.jsx("div",{className:"text-sm text-gray-500",children:t.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${ne(t.role)}`,children:t.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${t.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(t.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:xe(t)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>ye(t),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ds,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>ue(t),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(qs,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>se(t),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(Qe,{className:"h-4 w-4"})}),pe(t)&&e.jsx("button",{onClick:()=>ae(t),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx(De,{className:"h-4 w-4"})})]})})})]},t._id))})]})})}),h.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>H(h.current-1,w,j),disabled:h.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",h.current," of ",h.pages]}),e.jsx("button",{onClick:()=>H(h.current+1,w,j),disabled:h.current===h.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),F&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:G,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:v.name,onChange:t=>C({...v,name:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:v.email,onChange:t=>C({...v,email:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:v.password,onChange:t=>C({...v,password:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:v.role,onChange:t=>C({...v,role:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{S(!1),C({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),N&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:Q,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:v.name,onChange:t=>C({...v,name:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:v.email,onChange:t=>C({...v,email:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:v.role,onChange:t=>C({...v,role:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(R.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:v.isActive,onChange:t=>C({...v,isActive:t.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{p(!1),P(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),c&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:R.name})]}),e.jsxs("form",{onSubmit:re,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:U.newPassword,onChange:t=>_({...U,newPassword:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:U.confirmPassword,onChange:t=>_({...U,confirmPassword:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{b(!1),P(null),_({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),$&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Ws,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),R&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ne(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{A(!1),P(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:me,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(ge,{isOpen:M,onClose:()=>{L(!1),P(null)},title:"Manage User Permissions",children:R&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ne(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:I.canAddPlugins,onChange:t=>O(u=>({...u,canAddPlugins:t.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:I.canDeletePlugins,onChange:t=>O(u=>({...u,canDeletePlugins:t.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:I.canAddKeywords,onChange:t=>O(u=>({...u,canAddKeywords:t.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:I.canAddUsers,onChange:t=>O(u=>({...u,canAddUsers:t.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{L(!1),P(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),L(!1),P(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},At=()=>{const{user:s}=fe(),[l,r]=n.useState(!1),[y,i]=n.useState(""),[m,d]=n.useState(""),[w,T]=n.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});n.useEffect(()=>{j()},[]);const j=async()=>{try{const c=localStorage.getItem("adminToken"),A=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"}})).json();A.success&&T(A.permissions)}catch(c){console.error("Error loading permissions:",c)}},f=(c,b,$)=>{T(A=>({...A,[c]:{...A[c],[b]:$}}))},F=async()=>{r(!0),d(""),i("");try{const c=localStorage.getItem("adminToken"),A=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:w})})).json();A.success?(i("Permissions updated successfully"),setTimeout(()=>i(""),3e3)):d(A.message||"Failed to update permissions")}catch(c){console.error("Error saving permissions:",c),d("Failed to update permissions")}finally{r(!1)}},S={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},N={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Oe,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const p=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Qe,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),y&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:y}),m&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:m}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Ce,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:p?e.jsxs("button",{onClick:F,disabled:l,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(ms,{className:"h-4 w-4 mr-2"}),l?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(N).map(c=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:N[c]},c))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(S).map(c=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:S[c]}),Object.keys(N).map(b=>{var $;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:(($=w[b])==null?void 0:$[c])||!1,onChange:A=>f(b,c,A.target.checked),disabled:!p||b==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},b)})]},c))})]})})})]})]})},Pt=()=>{const{user:s}=fe(),[l,r]=n.useState(!1),[y,i]=n.useState(!1),[m,d]=n.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),w=async S=>{S.preventDefault(),i(!0);try{if(m.newPassword){if(m.newPassword!==m.confirmPassword){window.toast("New passwords do not match","error"),i(!1);return}if(m.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),i(!1);return}}const N=localStorage.getItem("adminToken"),p="https://pluginsight.vercel.app",c={name:m.name,email:m.email},b=await fetch(`${p}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${N}`,"Content-Type":"application/json"},body:JSON.stringify(c)});if(!b.ok)throw new Error(`HTTP error! status: ${b.status}`);const $=await b.json();if(!$.success){window.toast($.message||"Failed to update profile","error"),i(!1);return}if(m.newPassword){const M={newPassword:m.newPassword},L=await fetch(`${p}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${N}`,"Content-Type":"application/json"},body:JSON.stringify(M)});if(!L.ok)throw new Error(`HTTP error! status: ${L.status}`);const R=await L.json();if(!R.success){window.toast(R.message||"Failed to update password","error"),i(!1);return}}const A={...s,...$.user};localStorage.setItem("adminUser",JSON.stringify(A)),window.toast("Profile updated successfully","success"),r(!1),d(M=>({...M,name:A.name,email:A.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(N){console.error("Error updating profile:",N),N.name==="TypeError"&&N.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{i(!1)}},T=()=>{d({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),r(!1)},j=S=>{d({...m,[S.target.name]:S.target.value})},f=S=>{switch(S){case"superadmin":return e.jsx(Oe,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Oe,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(Pe,{className:"h-5 w-5 text-gray-600"})}},F=S=>{const N={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${N[S]}`,children:[f(S),e.jsx("span",{className:"ml-2 capitalize",children:S})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(Pe,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),F(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),l?e.jsxs("button",{onClick:T,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(Ve,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>r(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ds,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Pe,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:m.name,onChange:j,disabled:!l,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(is,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:m.email,onChange:j,disabled:!l,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Oe,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),l&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:m.newPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:m.confirmPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),l&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:y,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ms,{className:"h-4 w-4"}),e.jsx("span",{children:y?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},Ct=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],Dt={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function Rt(s){if(!s)return"";const l=document.createElement("div");l.innerHTML=s;function r(i){const m=i.tagName.toLowerCase();if(!Ct.includes(m)){i.remove();return}if(m==="iframe"){const j=i.getAttribute("src"),f=i.getAttribute("title")||"Video content",F=document.createElement("div");F.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let S="Video",N=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;j&&j.includes("youtube")?(S="YouTube Video",N=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):j&&j.includes("vimeo")&&(S="Vimeo Video",N=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),F.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${N}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${S}</h4>
            <p class="text-gray-600 text-sm mb-3">${f}</p>
            <a href="${j}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,i.parentNode.replaceChild(F,i);return}const d=Dt[m]||[];Array.from(i.attributes).forEach(j=>{d.includes(j.name)||i.removeAttribute(j.name)}),Array.from(i.children).forEach(j=>r(j))}return Array.from(l.children).forEach(i=>r(i)),l.innerHTML}function Ie(s){if(!s)return"";let l=s.replace(/\[video\s+([^\]]+)\]/g,(r,y)=>{const i=y.match(/src="([^"]+)"/);return i?`<video controls><source src="${i[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(r,y)=>{const i=y.match(/(?:id="|v=)([^"&\s]+)/);return i?`<iframe src="https://www.youtube.com/embed/${i[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(r,y)=>{const i=y.match(/id="?([^"\s]+)"?/);return i?`<iframe src="https://player.vimeo.com/video/${i[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(r,y)=>`<iframe src="https://www.youtube.com/embed/${y}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(r,y)=>`<iframe src="https://www.youtube.com/embed/${y}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(r,y)=>`<iframe src="https://player.vimeo.com/video/${y}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return Rt(l)}const $t=()=>{const{slug:s}=Ss(),l=Ke(),[r,y]=n.useState(null),[i,m]=n.useState(null),[d,w]=n.useState(!0),[T,j]=n.useState(""),[f,F]=n.useState({}),[S,N]=n.useState(""),[p,c]=n.useState(!0),[b,$]=n.useState(!1),A=async()=>{try{c(!0);const g=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${r.slug}`);if(!g.ok)throw new Error("Failed to fetch plugin information");const v=await g.json();v.versions&&F(v.versions)}catch(g){console.error("Error fetching versions data:",g),F({})}finally{c(!1)}};n.useEffect(()=>{r&&A()},[r]),n.useEffect(()=>{M()},[s]);const M=async()=>{try{w(!0);const g=localStorage.getItem("adminToken"),v="https://pluginsight.vercel.app",[C,U]=await Promise.all([fetch(`${v}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${g}`}}),fetch(`${v}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${g}`}})]);if(!C.ok)throw new Error("Failed to fetch plugin details");const _=await C.json();if(!_.success){j(_.message||"Plugin not found");return}if(y(_.plugin),U.ok){const I=await U.json();I.success&&I.pluginInfo?m(I.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(g){console.error("Error fetching plugin details:",g),j("Failed to load plugin details")}finally{w(!1)}},L=g=>{if(!g)return"N/A";const v=g.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!v)return"N/A";const[,C,U,_]=v;return`${_}-${U}-${C}`},R=g=>{const v=Math.round((g||0)/20);return[...Array(5)].map((C,U)=>e.jsx(He,{className:`h-4 w-4 ${U<v?"text-yellow-400 fill-current":"text-gray-300"}`},U))},P=g=>i&&i[g]!==void 0&&i[g]!==null?i[g]:r!=null&&r.pluginData&&r.pluginData[g]!==void 0&&r.pluginData[g]!==null?r.pluginData[g]:null,h=g=>{const v=g.split(".");if(i){let C=i;for(const U of v)if(C&&typeof C=="object"&&C[U]!==void 0)C=C[U];else{C=null;break}if(C!==null)return C}if(r!=null&&r.pluginData){let C=r.pluginData;for(const U of v)if(C&&typeof C=="object"&&C[U]!==void 0)C=C[U];else{C=null;break}return C}return null};return d?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):T||!r?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:T}),e.jsx("button",{onClick:()=>l(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>l(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Js,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[P("homepage")&&e.jsxs("button",{onClick:()=>window.open(P("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${r.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[h("icons.2x")&&e.jsx("img",{src:h("icons.2x"),alt:r.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:r.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[R(P("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",P("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(de,{className:"h-4 w-4"}),e.jsxs("span",{children:[(P("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),h("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(h("sections.description"))}})]}),h("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(h("sections.installation"))}})]}),h("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(h("sections.faq"))}})]}),h("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Ie(h("sections.changelog"))}})]}),P("screenshots")&&Object.keys(P("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(P("screenshots")).slice(0,6).map(([g,v])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:v.src,alt:v.caption||`Screenshot ${g}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),v.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:v.caption})]},g))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:P("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",r.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:L(P("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:L(P("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:P("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:P("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:P("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:P("active_installs")?`${P("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",P("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(P("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(de,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),p?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:S,onChange:g=>N(g.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(f).filter(([g])=>{var v;return g!==((v=r==null?void 0:r.pluginData)==null?void 0:v.version)}).sort((g,v)=>{const C=I=>I.split(".").map(Number),[U,_]=[C(g[0]),C(v[0])];for(let I=0;I<Math.max(U.length,_.length);I++){const O=(_[I]||0)-(U[I]||0);if(O!==0)return O}return 0}).slice(0,15).map(([g])=>e.jsxs("option",{value:g,children:["v",g]},g))]}),e.jsx("button",{onClick:g=>{g.preventDefault(),S&&(f!=null&&f[S])&&($(!0),window.open(f[S],"_blank"),setTimeout(()=>$(!1),2e3))},disabled:!S||b||p,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:b?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(de,{className:"h-4 w-4"})})})]})})]}),P("donate_link")&&e.jsxs("button",{onClick:()=>window.open(P("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(Ys,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),P("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Pe,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:P("author").replace(/<[^>]*>/g,"")}),P("author_profile")&&e.jsx("a",{href:P("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),P("tags")&&Object.keys(P("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(P("tags")).slice(0,10).map(g=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:g},g))})]}),P("contributors")&&Object.keys(P("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(P("contributors")).slice(0,10).map(([g,v])=>e.jsx("a",{href:v.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:v.avatar,alt:v.display_name||g,title:v.display_name||g,className:"w-8 h-8 rounded-full"})},g))})]})]})]})})]})};function Tt(){return e.jsx(it,{children:e.jsx(lt,{children:e.jsx(As,{children:e.jsxs(Ps,{children:[e.jsx(oe,{path:"/login",element:e.jsx(ht,{})}),e.jsxs(oe,{path:"/*",element:e.jsx(ct,{children:e.jsx(xt,{})}),children:[e.jsx(oe,{path:"dashboard",element:e.jsx(yt,{})})," ",e.jsx(oe,{path:"plugin-rank",element:e.jsx(bt,{})}),e.jsx(oe,{path:"keyword-analysis",element:e.jsx(jt,{})}),e.jsx(oe,{path:"analytics",element:e.jsx(Nt,{})}),e.jsx(oe,{path:"users",element:e.jsx(St,{})}),e.jsx(oe,{path:"settings",element:e.jsx(At,{})}),e.jsx(oe,{path:"profile",element:e.jsx(Pt,{})}),e.jsx(oe,{path:"plugin-details/:slug",element:e.jsx($t,{})}),e.jsx(oe,{path:"",element:e.jsx(ls,{to:"/dashboard",replace:!0})})]})]})})})})}ps(document.getElementById("root")).render(e.jsx(n.StrictMode,{children:e.jsx(Tt,{})}));
